"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./lib/i18n.ts":
/*!*********************!*\
  !*** ./lib/i18n.ts ***!
  \*********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var i18next__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! i18next */ \"(app-pages-browser)/./node_modules/i18next/dist/esm/i18next.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var i18next_browser_languagedetector__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! i18next-browser-languagedetector */ \"(app-pages-browser)/./node_modules/i18next-browser-languagedetector/dist/esm/i18nextBrowserLanguageDetector.js\");\n\n\n\nconst resources = {\n    ar: {\n        translation: {\n            // Navigation\n            \"dashboard\": \"لوحة التحكم\",\n            \"profile\": \"الملف الشخصي\",\n            \"requests\": \"الطلبات\",\n            \"tasks\": \"المهام\",\n            \"reports\": \"التقارير\",\n            \"settings\": \"الإعدادات\",\n            \"logout\": \"تسجيل الخروج\",\n            // Authentication\n            \"login\": \"تسجيل الدخول\",\n            \"register\": \"إنشاء حساب\",\n            \"email\": \"البريد الإلكتروني\",\n            \"password\": \"كلمة المرور\",\n            \"full_name\": \"الاسم الكامل\",\n            \"national_id\": \"رقم الهوية الوطنية\",\n            \"phone_number\": \"رقم الهاتف\",\n            \"login_tawtheeq\": \"تسجيل الدخول عبر توثيق\",\n            \"login_success\": \"تم تسجيل الدخول بنجاح\",\n            \"login_error\": \"خطأ في البيانات المدخلة\",\n            // Profile\n            \"personal_profile\": \"الملف الشخصي\",\n            \"basic_info\": \"المعلومات الأساسية\",\n            \"family_info\": \"بيانات الأسرة\",\n            \"employment_info\": \"بيانات العمل\",\n            \"financial_info\": \"المعلومات المالية\",\n            \"marital_status\": \"الحالة الاجتماعية\",\n            \"single\": \"أعزب\",\n            \"married\": \"متزوج\",\n            \"divorced\": \"مطلق\",\n            \"widowed\": \"أرمل\",\n            // Assistance Requests\n            \"assistance_request\": \"طلب المساعدة\",\n            \"new_request\": \"طلب جديد\",\n            \"request_type\": \"نوع المساعدة\",\n            \"requested_amount\": \"المبلغ المطلوب\",\n            \"description\": \"الوصف\",\n            \"attach_documents\": \"إرفاق المستندات\",\n            \"submit_request\": \"إرسال الطلب\",\n            // Status\n            \"draft\": \"مسودة\",\n            \"submitted\": \"مرسل\",\n            \"under_review\": \"قيد المراجعة\",\n            \"approved\": \"موافق عليه\",\n            \"rejected\": \"مرفوض\",\n            \"pending\": \"في الانتظار\",\n            \"completed\": \"مكتمل\",\n            // Workflow Stages\n            \"reception_review\": \"مراجعة الاستقبال\",\n            \"researcher_review\": \"مراجعة الباحث\",\n            \"banking_expert_review\": \"مراجعة الخبير المصرفي\",\n            \"department_head_review\": \"مراجعة رئيس القسم\",\n            \"admin_manager_review\": \"مراجعة مدير الإدارة\",\n            \"minister_review\": \"مراجعة الوزير\",\n            // Actions\n            \"approve\": \"موافقة\",\n            \"reject\": \"رفض\",\n            \"return\": \"إرجاع\",\n            \"save\": \"حفظ\",\n            \"edit\": \"تعديل\",\n            \"delete\": \"حذف\",\n            \"view\": \"عرض\",\n            \"download\": \"تحميل\",\n            \"upload\": \"رفع\",\n            // Common\n            \"welcome\": \"مرحباً\",\n            \"loading\": \"جاري التحميل...\",\n            \"search\": \"بحث\",\n            \"filter\": \"تصفية\",\n            \"date\": \"التاريخ\",\n            \"amount\": \"المبلغ\",\n            \"status\": \"الحالة\",\n            \"notes\": \"الملاحظات\",\n            \"documents\": \"المستندات\",\n            \"history\": \"التاريخ\",\n            // Dashboard Stats\n            \"total_requests\": \"إجمالي الطلبات\",\n            \"pending_review\": \"في انتظار المراجعة\",\n            \"approved_today\": \"موافق عليها اليوم\",\n            \"rejected_today\": \"مرفوضة اليوم\",\n            \"average_processing_days\": \"متوسط أيام المعالجة\",\n            \"total_users\": \"إجمالي المستخدمين\",\n            // User Roles\n            \"zakat_applicant\": \"مقدم طلب الزكاة\",\n            \"reception_staff\": \"موظف الاستقبال\",\n            \"researcher\": \"الباحث\",\n            \"banking_expert\": \"الخبير المصرفي\",\n            \"department_head\": \"رئيس القسم\",\n            \"admin_manager\": \"مدير الإدارة\",\n            \"minister\": \"الوزير\",\n            \"system_admin\": \"مسؤول النظام\",\n            // Beneficiary Management\n            \"beneficiaries\": \"المستفيدين\",\n            \"beneficiary_management\": \"إدارة المستفيدين\",\n            \"beneficiary_management_desc\": \"إدارة وتتبع المستفيدين من الزكاة والمساعدات\",\n            \"register_new_beneficiary\": \"تسجيل مستفيد جديد\",\n            \"beneficiary_registration\": \"تسجيل مستفيد جديد\",\n            \"beneficiary_registration_desc\": \"إضافة مستفيد جديد إلى نظام إدارة الزكاة\",\n            \"beneficiary_list\": \"قائمة المستفيدين\",\n            \"beneficiary_profile\": \"الملف الشخصي للمستفيد\",\n            \"beneficiary_details\": \"تفاصيل المستفيد\",\n            \"beneficiary_not_found\": \"المستفيد غير موجود\",\n            \"beneficiary_not_found_desc\": \"لم يتم العثور على المستفيد المطلوب\",\n            \"back_to_beneficiaries\": \"العودة إلى قائمة المستفيدين\",\n            // Beneficiary Stats\n            \"total_beneficiaries\": \"إجمالي المستفيدين\",\n            \"approved_beneficiaries\": \"المعتمدين\",\n            \"under_review_beneficiaries\": \"قيد المراجعة\",\n            \"total_distributions\": \"إجمالي التوزيعات\",\n            \"average_distribution\": \"متوسط التوزيع\",\n            \"pending_verification_count\": \"في انتظار التحقق\",\n            \"needs_review\": \"يحتاج إلى مراجعة\",\n            \"of_total\": \"من الإجمالي\",\n            \"per_beneficiary\": \"للمستفيد\",\n            // Search and Filters\n            \"search_and_filter\": \"البحث والتصفية\",\n            \"search_placeholder\": \"البحث بالاسم، رقم الهوية، أو رقم الهاتف...\",\n            \"filter_by_status\": \"تصفية بالحالة\",\n            \"filter_by_category\": \"تصفية بالفئة\",\n            \"all_statuses\": \"جميع الحالات\",\n            \"all_categories\": \"جميع الفئات\",\n            \"no_results_found\": \"لا توجد نتائج مطابقة لمعايير البحث\",\n            \"showing_results\": \"عرض {{count}} من أصل {{total}} مستفيد\",\n            \"export\": \"تصدير\",\n            // Beneficiary Status\n            \"pending_verification\": \"في انتظار التحقق\",\n            \"under_review\": \"قيد المراجعة\",\n            \"approved\": \"موافق عليه\",\n            \"rejected\": \"مرفوض\",\n            \"suspended\": \"معلق\",\n            \"inactive\": \"غير نشط\",\n            // Zakat Categories\n            \"fuqara\": \"الفقراء\",\n            \"masakin\": \"المساكين\",\n            \"amilin\": \"العاملين عليها\",\n            \"muallafah\": \"المؤلفة قلوبهم\",\n            \"riqab\": \"في الرقاب\",\n            \"gharimin\": \"الغارمين\",\n            \"fisabilillah\": \"في سبيل الله\",\n            \"ibnus_sabil\": \"ابن السبيل\",\n            \"primary_category\": \"الفئة الأساسية\",\n            // Table Headers\n            \"name\": \"الاسم\",\n            \"national_id\": \"رقم الهوية\",\n            \"registration_date\": \"تاريخ التسجيل\",\n            \"total_received\": \"إجمالي المستلم\",\n            \"actions\": \"الإجراءات\",\n            // Actions\n            \"view_profile\": \"عرض الملف الشخصي\",\n            \"edit\": \"تعديل\",\n            \"manage_case\": \"إدارة الحالة\",\n            \"generate_voucher\": \"إنشاء قسيمة\",\n            \"back\": \"العودة\",\n            // Profile Tabs\n            \"overview\": \"نظرة عامة\",\n            \"personal_details\": \"البيانات الشخصية\",\n            \"eligibility_verification\": \"الأهلية والتحقق\",\n            \"case_management\": \"إدارة الحالة\",\n            \"distribution_history\": \"سجل التوزيعات\",\n            \"family\": \"الأسرة\",\n            \"documents\": \"المستندات\",\n            // Profile Details\n            \"eligibility_score\": \"نقاط الأهلية\",\n            \"high_score\": \"درجة عالية\",\n            \"family_size\": \"حجم الأسرة\",\n            \"dependents\": \"معالين\",\n            \"last_distribution\": \"آخر توزيع\",\n            \"no_distribution\": \"لا يوجد\",\n            \"not_distributed_yet\": \"لم يتم التوزيع بعد\",\n            \"distributions\": \"توزيعات\",\n            // Contact Information\n            \"contact_info\": \"معلومات الاتصال\",\n            \"account_status\": \"حالة الحساب\",\n            \"current_status\": \"الحالة الحالية\",\n            \"registration_date\": \"تاريخ التسجيل\",\n            \"next_review\": \"المراجعة القادمة\",\n            // Personal Information\n            \"personal_information\": \"البيانات الشخصية\",\n            \"basic_information\": \"المعلومات الأساسية\",\n            \"name_arabic\": \"الاسم بالعربية\",\n            \"name_english\": \"الاسم بالإنجليزية\",\n            \"date_of_birth\": \"تاريخ الميلاد\",\n            \"gender\": \"الجنس\",\n            \"male\": \"ذكر\",\n            \"female\": \"أنثى\",\n            \"marital_status\": \"الحالة الاجتماعية\",\n            \"single\": \"أعزب\",\n            \"married\": \"متزوج\",\n            \"divorced\": \"مطلق\",\n            \"widowed\": \"أرمل\",\n            // Family Members\n            \"family_members\": \"أفراد الأسرة\",\n            \"no_family_info\": \"لا توجد معلومات عن أفراد الأسرة\",\n            \"dependent\": \"معال\",\n            \"special_needs\": \"احتياجات خاصة\",\n            \"relationship\": \"القرابة\",\n            \"age\": \"العمر\",\n            \"years\": \"سنة\",\n            \"son\": \"ابن\",\n            \"daughter\": \"ابنة\",\n            \"mother\": \"أم\",\n            \"father\": \"أب\",\n            // Documents\n            \"no_documents\": \"لا توجد مستندات مرفوعة\",\n            \"verified\": \"محقق\",\n            \"pending_verification\": \"في انتظار التحقق\",\n            \"upload_date\": \"تاريخ الرفع\",\n            // Coming Soon\n            \"coming_soon\": \"قريباً\",\n            \"under_development\": \"قيد التطوير\",\n            \"registration_form_coming\": \"نموذج التسجيل قيد التطوير\",\n            \"registration_form_desc\": \"سيتم إضافة نموذج تسجيل المستفيدين الجدد قريباً\",\n            \"will_include_features\": \"سيتضمن النموذج الميزات التالية:\",\n            \"multi_step_form\": \"نموذج متعدد الخطوات مع مؤشر التقدم\",\n            \"dual_language_input\": \"إدخال البيانات الشخصية بالعربية والإنجليزية\",\n            \"zakat_categories_selection\": \"اختيار فئات الزكاة الثمانية\",\n            \"document_upload\": \"رفع المستندات المطلوبة\",\n            \"data_validation\": \"التحقق من صحة البيانات\",\n            \"duplicate_detection\": \"كشف التكرار التلقائي\",\n            // System Information\n            \"system_name\": \"نظام إدارة الزكاة\",\n            \"system_description\": \"نظام شامل لإدارة طلبات الزكاة والمساعدات\",\n            // Authentication Messages\n            \"create_new_account\": \"إنشاء حساب جديد\",\n            \"sign_in_to_account\": \"تسجيل الدخول إلى حسابك\",\n            \"choose_login_method\": \"اختر طريقة تسجيل الدخول المناسبة\",\n            \"verifying\": \"جاري التحقق...\",\n            \"error\": \"خطأ\",\n            \"passwords_not_match\": \"كلمات المرور غير متطابقة\",\n            \"account_created_success\": \"تم إنشاء الحساب بنجاح\",\n            \"wait_admin_approval\": \"يرجى انتظار موافقة الإدارة على حسابك\",\n            \"account_creation_error\": \"خطأ في إنشاء الحساب\",\n            \"unexpected_error\": \"حدث خطأ غير متوقع\",\n            \"error_during_creation\": \"حدث خطأ أثناء إنشاء الحساب\",\n            \"invalid_credentials\": \"البيانات المدخلة غير صحيحة\",\n            \"welcome_to_system\": \"مرحباً بك في نظام إدارة الزكاة\",\n            \"error_during_login\": \"حدث خطأ أثناء تسجيل الدخول\",\n            // Account Status\n            \"account_pending_approval\": \"حسابك في انتظار الموافقة\",\n            \"wait_admin_approval_desc\": \"يرجى انتظار موافقة الإدارة على حسابك\",\n            // Dashboard\n            \"overview_status\": \"نظرة عامة على حالة\",\n            \"your_requests\": \"طلباتك\",\n            \"assigned_tasks\": \"المهام المخصصة لك\",\n            \"total_requests_desc\": \"إجمالي الطلبات\",\n            \"pending_review_desc\": \"في انتظار المراجعة\",\n            \"approved_today_desc\": \"موافق عليها اليوم\",\n            \"avg_processing_days_desc\": \"متوسط أيام المعالجة\",\n            // Reports\n            \"no_reports_access\": \"ليس لديك صلاحية للوصول إلى التقارير\",\n            \"monthly_report\": \"التقرير الشهري\",\n            \"monthly_stats_desc\": \"إحصائيات الطلبات والموافقات الشهرية\",\n            \"requests_label\": \"الطلبات\",\n            \"approved_label\": \"موافق\",\n            \"rejected_label\": \"مرفوض\",\n            // Requests\n            \"back_button\": \"العودة\",\n            \"request_details\": \"تفاصيل الطلب\",\n            \"download_decision\": \"تحميل القرار\",\n            // Gender and Personal Info\n            \"gender_label\": \"الجنس\",\n            \"male_label\": \"ذكر\",\n            \"female_label\": \"أنثى\",\n            \"marital_status_label\": \"الحالة الاجتماعية\",\n            \"married_label\": \"متزوج\",\n            \"single_label\": \"أعزب\",\n            \"divorced_label\": \"مطلق\",\n            \"widowed_label\": \"أرمل\",\n            // Common UI Text\n            \"or\": \"أو\",\n            \"no_account\": \"ليس لديك حساب؟\",\n            \"demo_accounts\": \"حسابات تجريبية:\",\n            \"system_admin\": \"مسؤول النظام:\",\n            \"applicant\": \"مقدم طلب:\",\n            \"staff_member\": \"موظف:\",\n            // Access Control\n            \"access_denied\": \"غير مصرح\",\n            \"no_beneficiary_access\": \"ليس لديك صلاحية للوصول إلى إدارة المستفيدين\",\n            \"no_registration_access\": \"ليس لديك صلاحية لتسجيل مستفيدين جدد\"\n        }\n    },\n    en: {\n        translation: {\n            // Navigation\n            \"dashboard\": \"Dashboard\",\n            \"profile\": \"Profile\",\n            \"requests\": \"Requests\",\n            \"tasks\": \"Tasks\",\n            \"reports\": \"Reports\",\n            \"settings\": \"Settings\",\n            \"logout\": \"Logout\",\n            // Authentication\n            \"login\": \"Login\",\n            \"register\": \"Register\",\n            \"email\": \"Email\",\n            \"password\": \"Password\",\n            \"full_name\": \"Full Name\",\n            \"national_id\": \"National ID\",\n            \"phone_number\": \"Phone Number\",\n            \"login_tawtheeq\": \"Login with Tawtheeq\",\n            \"login_success\": \"Login successful\",\n            \"login_error\": \"Invalid credentials\",\n            // Profile\n            \"personal_profile\": \"Personal Profile\",\n            \"basic_info\": \"Basic Information\",\n            \"family_info\": \"Family Information\",\n            \"employment_info\": \"Employment Information\",\n            \"financial_info\": \"Financial Information\",\n            \"marital_status\": \"Marital Status\",\n            \"single\": \"Single\",\n            \"married\": \"Married\",\n            \"divorced\": \"Divorced\",\n            \"widowed\": \"Widowed\",\n            // Assistance Requests\n            \"assistance_request\": \"Assistance Request\",\n            \"new_request\": \"New Request\",\n            \"request_type\": \"Request Type\",\n            \"requested_amount\": \"Requested Amount\",\n            \"description\": \"Description\",\n            \"attach_documents\": \"Attach Documents\",\n            \"submit_request\": \"Submit Request\",\n            // Status\n            \"draft\": \"Draft\",\n            \"submitted\": \"Submitted\",\n            \"under_review\": \"Under Review\",\n            \"approved\": \"Approved\",\n            \"rejected\": \"Rejected\",\n            \"pending\": \"Pending\",\n            \"completed\": \"Completed\",\n            // Workflow Stages\n            \"reception_review\": \"Reception Review\",\n            \"researcher_review\": \"Researcher Review\",\n            \"banking_expert_review\": \"Banking Expert Review\",\n            \"department_head_review\": \"Department Head Review\",\n            \"admin_manager_review\": \"Admin Manager Review\",\n            \"minister_review\": \"Minister Review\",\n            // Actions\n            \"approve\": \"Approve\",\n            \"reject\": \"Reject\",\n            \"return\": \"Return\",\n            \"save\": \"Save\",\n            \"edit\": \"Edit\",\n            \"delete\": \"Delete\",\n            \"view\": \"View\",\n            \"download\": \"Download\",\n            \"upload\": \"Upload\",\n            // Common\n            \"welcome\": \"Welcome\",\n            \"loading\": \"Loading...\",\n            \"search\": \"Search\",\n            \"filter\": \"Filter\",\n            \"date\": \"Date\",\n            \"amount\": \"Amount\",\n            \"status\": \"Status\",\n            \"notes\": \"Notes\",\n            \"documents\": \"Documents\",\n            \"history\": \"History\",\n            // Dashboard Stats\n            \"total_requests\": \"Total Requests\",\n            \"pending_review\": \"Pending Review\",\n            \"approved_today\": \"Approved Today\",\n            \"rejected_today\": \"Rejected Today\",\n            \"average_processing_days\": \"Avg. Processing Days\",\n            \"total_users\": \"Total Users\",\n            // User Roles\n            \"zakat_applicant\": \"Zakat Applicant\",\n            \"reception_staff\": \"Reception Staff\",\n            \"researcher\": \"Researcher\",\n            \"banking_expert\": \"Banking Expert\",\n            \"department_head\": \"Department Head\",\n            \"admin_manager\": \"Administration Manager\",\n            \"minister\": \"Minister\",\n            \"system_admin\": \"System Administrator\",\n            // Beneficiary Management\n            \"beneficiaries\": \"Beneficiaries\",\n            \"beneficiary_management\": \"Beneficiary Management\",\n            \"beneficiary_management_desc\": \"Manage and track Zakat and assistance beneficiaries\",\n            \"register_new_beneficiary\": \"Register New Beneficiary\",\n            \"beneficiary_registration\": \"New Beneficiary Registration\",\n            \"beneficiary_registration_desc\": \"Add a new beneficiary to the Zakat management system\",\n            \"beneficiary_list\": \"Beneficiary List\",\n            \"beneficiary_profile\": \"Beneficiary Profile\",\n            \"beneficiary_details\": \"Beneficiary Details\",\n            \"beneficiary_not_found\": \"Beneficiary Not Found\",\n            \"beneficiary_not_found_desc\": \"The requested beneficiary could not be found\",\n            \"back_to_beneficiaries\": \"Back to Beneficiaries List\",\n            // Beneficiary Stats\n            \"total_beneficiaries\": \"Total Beneficiaries\",\n            \"approved_beneficiaries\": \"Approved\",\n            \"under_review_beneficiaries\": \"Under Review\",\n            \"total_distributions\": \"Total Distributions\",\n            \"average_distribution\": \"Average Distribution\",\n            \"pending_verification_count\": \"Pending Verification\",\n            \"needs_review\": \"Needs Review\",\n            \"of_total\": \"of Total\",\n            \"per_beneficiary\": \"per Beneficiary\",\n            // Search and Filters\n            \"search_and_filter\": \"Search and Filter\",\n            \"search_placeholder\": \"Search by name, national ID, or phone number...\",\n            \"filter_by_status\": \"Filter by Status\",\n            \"filter_by_category\": \"Filter by Category\",\n            \"all_statuses\": \"All Statuses\",\n            \"all_categories\": \"All Categories\",\n            \"no_results_found\": \"No results found matching search criteria\",\n            \"showing_results\": \"Showing {{count}} of {{total}} beneficiaries\",\n            \"export\": \"Export\",\n            // Beneficiary Status\n            \"pending_verification\": \"Pending Verification\",\n            \"under_review\": \"Under Review\",\n            \"approved\": \"Approved\",\n            \"rejected\": \"Rejected\",\n            \"suspended\": \"Suspended\",\n            \"inactive\": \"Inactive\",\n            // Zakat Categories\n            \"fuqara\": \"The Poor\",\n            \"masakin\": \"The Needy\",\n            \"amilin\": \"Zakat Administrators\",\n            \"muallafah\": \"Those whose hearts are reconciled\",\n            \"riqab\": \"To free slaves/captives\",\n            \"gharimin\": \"Those in debt\",\n            \"fisabilillah\": \"In the cause of Allah\",\n            \"ibnus_sabil\": \"The wayfarer/traveler\",\n            \"primary_category\": \"Primary Category\",\n            // Table Headers\n            \"name\": \"Name\",\n            \"national_id\": \"National ID\",\n            \"registration_date\": \"Registration Date\",\n            \"total_received\": \"Total Received\",\n            \"actions\": \"Actions\",\n            // Actions\n            \"view_profile\": \"View Profile\",\n            \"edit\": \"Edit\",\n            \"manage_case\": \"Manage Case\",\n            \"generate_voucher\": \"Generate Voucher\",\n            \"back\": \"Back\",\n            // Profile Tabs\n            \"overview\": \"Overview\",\n            \"personal_details\": \"Personal Details\",\n            \"eligibility_verification\": \"Eligibility & Verification\",\n            \"case_management\": \"Case Management\",\n            \"distribution_history\": \"Distribution History\",\n            \"family\": \"Family\",\n            \"documents\": \"Documents\",\n            // Profile Details\n            \"eligibility_score\": \"Eligibility Score\",\n            \"high_score\": \"High Score\",\n            \"family_size\": \"Family Size\",\n            \"dependents\": \"Dependents\",\n            \"last_distribution\": \"Last Distribution\",\n            \"no_distribution\": \"None\",\n            \"not_distributed_yet\": \"Not distributed yet\",\n            \"distributions\": \"Distributions\",\n            // Contact Information\n            \"contact_info\": \"Contact Information\",\n            \"account_status\": \"Account Status\",\n            \"current_status\": \"Current Status\",\n            \"registration_date\": \"Registration Date\",\n            \"next_review\": \"Next Review\",\n            // Personal Information\n            \"personal_information\": \"Personal Information\",\n            \"basic_information\": \"Basic Information\",\n            \"name_arabic\": \"Name in Arabic\",\n            \"name_english\": \"Name in English\",\n            \"date_of_birth\": \"Date of Birth\",\n            \"gender\": \"Gender\",\n            \"male\": \"Male\",\n            \"female\": \"Female\",\n            \"marital_status\": \"Marital Status\",\n            \"single\": \"Single\",\n            \"married\": \"Married\",\n            \"divorced\": \"Divorced\",\n            \"widowed\": \"Widowed\",\n            // Family Members\n            \"family_members\": \"Family Members\",\n            \"no_family_info\": \"No family information available\",\n            \"dependent\": \"Dependent\",\n            \"special_needs\": \"Special Needs\",\n            \"relationship\": \"Relationship\",\n            \"age\": \"Age\",\n            \"years\": \"years\",\n            \"son\": \"Son\",\n            \"daughter\": \"Daughter\",\n            \"mother\": \"Mother\",\n            \"father\": \"Father\",\n            // Documents\n            \"no_documents\": \"No documents uploaded\",\n            \"verified\": \"Verified\",\n            \"pending_verification\": \"Pending Verification\",\n            \"upload_date\": \"Upload Date\",\n            // Coming Soon\n            \"coming_soon\": \"Coming Soon\",\n            \"under_development\": \"Under Development\",\n            \"registration_form_coming\": \"Registration Form Under Development\",\n            \"registration_form_desc\": \"New beneficiary registration form will be added soon\",\n            \"will_include_features\": \"The form will include the following features:\",\n            \"multi_step_form\": \"Multi-step form with progress indicator\",\n            \"dual_language_input\": \"Personal data input in Arabic and English\",\n            \"zakat_categories_selection\": \"Selection of eight Zakat categories\",\n            \"document_upload\": \"Required document upload\",\n            \"data_validation\": \"Data validation\",\n            \"duplicate_detection\": \"Automatic duplicate detection\",\n            // System Information\n            \"system_name\": \"Zakat Management System\",\n            \"system_description\": \"Comprehensive system for managing Zakat and assistance requests\",\n            // Authentication Messages\n            \"create_new_account\": \"Create new account\",\n            \"sign_in_to_account\": \"Sign in to your account\",\n            \"choose_login_method\": \"Choose the appropriate login method\",\n            \"verifying\": \"Verifying...\",\n            \"error\": \"Error\",\n            \"passwords_not_match\": \"Passwords do not match\",\n            \"account_created_success\": \"Account created successfully\",\n            \"wait_admin_approval\": \"Please wait for admin approval of your account\",\n            \"account_creation_error\": \"Error creating account\",\n            \"unexpected_error\": \"An unexpected error occurred\",\n            \"error_during_creation\": \"An error occurred during account creation\",\n            \"invalid_credentials\": \"Invalid credentials entered\",\n            \"welcome_to_system\": \"Welcome to the Zakat Management System\",\n            \"error_during_login\": \"An error occurred during login\",\n            // Account Status\n            \"account_pending_approval\": \"Your account is pending approval\",\n            \"wait_admin_approval_desc\": \"Please wait for admin approval of your account\",\n            // Dashboard\n            \"overview_status\": \"Overview of status\",\n            \"your_requests\": \"your requests\",\n            \"assigned_tasks\": \"tasks assigned to you\",\n            \"total_requests_desc\": \"Total requests\",\n            \"pending_review_desc\": \"Pending review\",\n            \"approved_today_desc\": \"Approved today\",\n            \"avg_processing_days_desc\": \"Average processing days\",\n            // Reports\n            \"no_reports_access\": \"You do not have permission to access reports\",\n            \"monthly_report\": \"Monthly Report\",\n            \"monthly_stats_desc\": \"Monthly statistics of requests and approvals\",\n            \"requests_label\": \"Requests\",\n            \"approved_label\": \"Approved\",\n            \"rejected_label\": \"Rejected\",\n            // Requests\n            \"back_button\": \"Back\",\n            \"request_details\": \"Request Details\",\n            \"download_decision\": \"Download Decision\",\n            // Gender and Personal Info\n            \"gender_label\": \"Gender\",\n            \"male_label\": \"Male\",\n            \"female_label\": \"Female\",\n            \"marital_status_label\": \"Marital Status\",\n            \"married_label\": \"Married\",\n            \"single_label\": \"Single\",\n            \"divorced_label\": \"Divorced\",\n            \"widowed_label\": \"Widowed\",\n            // Access Control\n            \"access_denied\": \"Access Denied\",\n            \"no_beneficiary_access\": \"You do not have permission to access beneficiary management\",\n            \"no_registration_access\": \"You do not have permission to register new beneficiaries\"\n        }\n    }\n};\n// Initialize i18n immediately with resources\ni18next__WEBPACK_IMPORTED_MODULE_0__[\"default\"].use(i18next_browser_languagedetector__WEBPACK_IMPORTED_MODULE_2__[\"default\"]).use(react_i18next__WEBPACK_IMPORTED_MODULE_1__.initReactI18next).init({\n    resources,\n    fallbackLng: \"ar\",\n    lng: \"ar\",\n    debug: \"development\" === \"development\",\n    detection: {\n        order: [\n            \"localStorage\",\n            \"navigator\",\n            \"htmlTag\"\n        ],\n        caches: [\n            \"localStorage\"\n        ]\n    },\n    interpolation: {\n        escapeValue: false\n    }\n});\n/* harmony default export */ __webpack_exports__[\"default\"] = (i18next__WEBPACK_IMPORTED_MODULE_0__[\"default\"]);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/i18n.ts\n"));

/***/ })

});