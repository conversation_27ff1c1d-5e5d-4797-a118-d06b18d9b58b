"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./lib/i18n.ts":
/*!*********************!*\
  !*** ./lib/i18n.ts ***!
  \*********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var i18next__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! i18next */ \"(app-pages-browser)/./node_modules/i18next/dist/esm/i18next.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var i18next_browser_languagedetector__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! i18next-browser-languagedetector */ \"(app-pages-browser)/./node_modules/i18next-browser-languagedetector/dist/esm/i18nextBrowserLanguageDetector.js\");\n\n\n\nconst resources = {\n    ar: {\n        translation: {\n            // Navigation\n            \"dashboard\": \"لوحة التحكم\",\n            \"profile\": \"الملف الشخصي\",\n            \"requests\": \"الطلبات\",\n            \"tasks\": \"المهام\",\n            \"reports\": \"التقارير\",\n            \"settings\": \"الإعدادات\",\n            \"logout\": \"تسجيل الخروج\",\n            // Authentication\n            \"login\": \"تسجيل الدخول\",\n            \"register\": \"إنشاء حساب\",\n            \"email\": \"البريد الإلكتروني\",\n            \"password\": \"كلمة المرور\",\n            \"full_name\": \"الاسم الكامل\",\n            \"national_id\": \"رقم الهوية الوطنية\",\n            \"phone_number\": \"رقم الهاتف\",\n            \"login_tawtheeq\": \"تسجيل الدخول عبر توثيق\",\n            \"login_success\": \"تم تسجيل الدخول بنجاح\",\n            \"login_error\": \"خطأ في البيانات المدخلة\",\n            // Profile\n            \"personal_profile\": \"الملف الشخصي\",\n            \"basic_info\": \"المعلومات الأساسية\",\n            \"family_info\": \"بيانات الأسرة\",\n            \"employment_info\": \"بيانات العمل\",\n            \"financial_info\": \"المعلومات المالية\",\n            \"marital_status\": \"الحالة الاجتماعية\",\n            \"single\": \"أعزب\",\n            \"married\": \"متزوج\",\n            \"divorced\": \"مطلق\",\n            \"widowed\": \"أرمل\",\n            // Assistance Requests\n            \"assistance_request\": \"طلب المساعدة\",\n            \"new_request\": \"طلب جديد\",\n            \"request_type\": \"نوع المساعدة\",\n            \"requested_amount\": \"المبلغ المطلوب\",\n            \"description\": \"الوصف\",\n            \"attach_documents\": \"إرفاق المستندات\",\n            \"submit_request\": \"إرسال الطلب\",\n            // Status\n            \"draft\": \"مسودة\",\n            \"submitted\": \"مرسل\",\n            \"under_review\": \"قيد المراجعة\",\n            \"approved\": \"موافق عليه\",\n            \"rejected\": \"مرفوض\",\n            \"pending\": \"في الانتظار\",\n            \"completed\": \"مكتمل\",\n            // Workflow Stages\n            \"reception_review\": \"مراجعة الاستقبال\",\n            \"researcher_review\": \"مراجعة الباحث\",\n            \"banking_expert_review\": \"مراجعة الخبير المصرفي\",\n            \"department_head_review\": \"مراجعة رئيس القسم\",\n            \"admin_manager_review\": \"مراجعة مدير الإدارة\",\n            \"minister_review\": \"مراجعة الوزير\",\n            // Actions\n            \"approve\": \"موافقة\",\n            \"reject\": \"رفض\",\n            \"return\": \"إرجاع\",\n            \"save\": \"حفظ\",\n            \"edit\": \"تعديل\",\n            \"delete\": \"حذف\",\n            \"view\": \"عرض\",\n            \"download\": \"تحميل\",\n            \"upload\": \"رفع\",\n            // Common\n            \"welcome\": \"مرحباً\",\n            \"loading\": \"جاري التحميل...\",\n            \"search\": \"بحث\",\n            \"filter\": \"تصفية\",\n            \"date\": \"التاريخ\",\n            \"amount\": \"المبلغ\",\n            \"status\": \"الحالة\",\n            \"notes\": \"الملاحظات\",\n            \"documents\": \"المستندات\",\n            \"history\": \"التاريخ\",\n            // Dashboard Stats\n            \"total_requests\": \"إجمالي الطلبات\",\n            \"pending_review\": \"في انتظار المراجعة\",\n            \"approved_today\": \"موافق عليها اليوم\",\n            \"rejected_today\": \"مرفوضة اليوم\",\n            \"average_processing_days\": \"متوسط أيام المعالجة\",\n            \"total_users\": \"إجمالي المستخدمين\",\n            // User Roles\n            \"zakat_applicant\": \"مقدم طلب الزكاة\",\n            \"reception_staff\": \"موظف الاستقبال\",\n            \"researcher\": \"الباحث\",\n            \"banking_expert\": \"الخبير المصرفي\",\n            \"department_head\": \"رئيس القسم\",\n            \"admin_manager\": \"مدير الإدارة\",\n            \"minister\": \"الوزير\",\n            \"system_admin\": \"مسؤول النظام\",\n            // Beneficiary Management\n            \"beneficiaries\": \"المستفيدين\",\n            \"beneficiary_management\": \"إدارة المستفيدين\",\n            \"beneficiary_management_desc\": \"إدارة وتتبع المستفيدين من الزكاة والمساعدات\",\n            \"register_new_beneficiary\": \"تسجيل مستفيد جديد\",\n            \"beneficiary_registration\": \"تسجيل مستفيد جديد\",\n            \"beneficiary_registration_desc\": \"إضافة مستفيد جديد إلى نظام إدارة الزكاة\",\n            \"beneficiary_list\": \"قائمة المستفيدين\",\n            \"beneficiary_profile\": \"الملف الشخصي للمستفيد\",\n            \"beneficiary_details\": \"تفاصيل المستفيد\",\n            \"beneficiary_not_found\": \"المستفيد غير موجود\",\n            \"beneficiary_not_found_desc\": \"لم يتم العثور على المستفيد المطلوب\",\n            \"back_to_beneficiaries\": \"العودة إلى قائمة المستفيدين\",\n            // Beneficiary Stats\n            \"total_beneficiaries\": \"إجمالي المستفيدين\",\n            \"approved_beneficiaries\": \"المعتمدين\",\n            \"under_review_beneficiaries\": \"قيد المراجعة\",\n            \"total_distributions\": \"إجمالي التوزيعات\",\n            \"average_distribution\": \"متوسط التوزيع\",\n            \"pending_verification_count\": \"في انتظار التحقق\",\n            \"needs_review\": \"يحتاج إلى مراجعة\",\n            \"of_total\": \"من الإجمالي\",\n            \"per_beneficiary\": \"للمستفيد\",\n            // Search and Filters\n            \"search_and_filter\": \"البحث والتصفية\",\n            \"search_placeholder\": \"البحث بالاسم، رقم الهوية، أو رقم الهاتف...\",\n            \"filter_by_status\": \"تصفية بالحالة\",\n            \"filter_by_category\": \"تصفية بالفئة\",\n            \"all_statuses\": \"جميع الحالات\",\n            \"all_categories\": \"جميع الفئات\",\n            \"no_results_found\": \"لا توجد نتائج مطابقة لمعايير البحث\",\n            \"showing_results\": \"عرض {{count}} من أصل {{total}} مستفيد\",\n            \"export\": \"تصدير\",\n            // Beneficiary Status\n            \"pending_verification\": \"في انتظار التحقق\",\n            \"under_review\": \"قيد المراجعة\",\n            \"approved\": \"موافق عليه\",\n            \"rejected\": \"مرفوض\",\n            \"suspended\": \"معلق\",\n            \"inactive\": \"غير نشط\",\n            // Zakat Categories\n            \"fuqara\": \"الفقراء\",\n            \"masakin\": \"المساكين\",\n            \"amilin\": \"العاملين عليها\",\n            \"muallafah\": \"المؤلفة قلوبهم\",\n            \"riqab\": \"في الرقاب\",\n            \"gharimin\": \"الغارمين\",\n            \"fisabilillah\": \"في سبيل الله\",\n            \"ibnus_sabil\": \"ابن السبيل\",\n            \"primary_category\": \"الفئة الأساسية\",\n            // Table Headers\n            \"name\": \"الاسم\",\n            \"national_id\": \"رقم الهوية\",\n            \"registration_date\": \"تاريخ التسجيل\",\n            \"total_received\": \"إجمالي المستلم\",\n            \"actions\": \"الإجراءات\",\n            // Actions\n            \"view_profile\": \"عرض الملف الشخصي\",\n            \"edit\": \"تعديل\",\n            \"manage_case\": \"إدارة الحالة\",\n            \"generate_voucher\": \"إنشاء قسيمة\",\n            \"back\": \"العودة\",\n            // Profile Tabs\n            \"overview\": \"نظرة عامة\",\n            \"personal_details\": \"البيانات الشخصية\",\n            \"eligibility_verification\": \"الأهلية والتحقق\",\n            \"case_management\": \"إدارة الحالة\",\n            \"distribution_history\": \"سجل التوزيعات\",\n            \"family\": \"الأسرة\",\n            \"documents\": \"المستندات\",\n            // Profile Details\n            \"eligibility_score\": \"نقاط الأهلية\",\n            \"high_score\": \"درجة عالية\",\n            \"family_size\": \"حجم الأسرة\",\n            \"dependents\": \"معالين\",\n            \"last_distribution\": \"آخر توزيع\",\n            \"no_distribution\": \"لا يوجد\",\n            \"not_distributed_yet\": \"لم يتم التوزيع بعد\",\n            \"distributions\": \"توزيعات\",\n            // Contact Information\n            \"contact_info\": \"معلومات الاتصال\",\n            \"account_status\": \"حالة الحساب\",\n            \"current_status\": \"الحالة الحالية\",\n            \"registration_date\": \"تاريخ التسجيل\",\n            \"next_review\": \"المراجعة القادمة\",\n            // Personal Information\n            \"personal_information\": \"البيانات الشخصية\",\n            \"basic_information\": \"المعلومات الأساسية\",\n            \"name_arabic\": \"الاسم بالعربية\",\n            \"name_english\": \"الاسم بالإنجليزية\",\n            \"date_of_birth\": \"تاريخ الميلاد\",\n            \"gender\": \"الجنس\",\n            \"male\": \"ذكر\",\n            \"female\": \"أنثى\",\n            \"marital_status\": \"الحالة الاجتماعية\",\n            \"single\": \"أعزب\",\n            \"married\": \"متزوج\",\n            \"divorced\": \"مطلق\",\n            \"widowed\": \"أرمل\",\n            // Family Members\n            \"family_members\": \"أفراد الأسرة\",\n            \"no_family_info\": \"لا توجد معلومات عن أفراد الأسرة\",\n            \"dependent\": \"معال\",\n            \"special_needs\": \"احتياجات خاصة\",\n            \"relationship\": \"القرابة\",\n            \"age\": \"العمر\",\n            \"years\": \"سنة\",\n            \"son\": \"ابن\",\n            \"daughter\": \"ابنة\",\n            \"mother\": \"أم\",\n            \"father\": \"أب\",\n            // Documents\n            \"no_documents\": \"لا توجد مستندات مرفوعة\",\n            \"verified\": \"محقق\",\n            \"pending_verification\": \"في انتظار التحقق\",\n            \"upload_date\": \"تاريخ الرفع\",\n            // Coming Soon\n            \"coming_soon\": \"قريباً\",\n            \"under_development\": \"قيد التطوير\",\n            \"registration_form_coming\": \"نموذج التسجيل قيد التطوير\",\n            \"registration_form_desc\": \"سيتم إضافة نموذج تسجيل المستفيدين الجدد قريباً\",\n            \"will_include_features\": \"سيتضمن النموذج الميزات التالية:\",\n            \"multi_step_form\": \"نموذج متعدد الخطوات مع مؤشر التقدم\",\n            \"dual_language_input\": \"إدخال البيانات الشخصية بالعربية والإنجليزية\",\n            \"zakat_categories_selection\": \"اختيار فئات الزكاة الثمانية\",\n            \"document_upload\": \"رفع المستندات المطلوبة\",\n            \"data_validation\": \"التحقق من صحة البيانات\",\n            \"duplicate_detection\": \"كشف التكرار التلقائي\",\n            // System Information\n            \"system_name\": \"نظام إدارة الزكاة\",\n            \"system_description\": \"نظام شامل لإدارة طلبات الزكاة والمساعدات\",\n            // Authentication Messages\n            \"create_new_account\": \"إنشاء حساب جديد\",\n            \"sign_in_to_account\": \"تسجيل الدخول إلى حسابك\",\n            \"choose_login_method\": \"اختر طريقة تسجيل الدخول المناسبة\",\n            \"verifying\": \"جاري التحقق...\",\n            \"error\": \"خطأ\",\n            \"passwords_not_match\": \"كلمات المرور غير متطابقة\",\n            \"account_created_success\": \"تم إنشاء الحساب بنجاح\",\n            \"wait_admin_approval\": \"يرجى انتظار موافقة الإدارة على حسابك\",\n            \"account_creation_error\": \"خطأ في إنشاء الحساب\",\n            \"unexpected_error\": \"حدث خطأ غير متوقع\",\n            \"error_during_creation\": \"حدث خطأ أثناء إنشاء الحساب\",\n            \"invalid_credentials\": \"البيانات المدخلة غير صحيحة\",\n            \"welcome_to_system\": \"مرحباً بك في نظام إدارة الزكاة\",\n            \"error_during_login\": \"حدث خطأ أثناء تسجيل الدخول\",\n            // Account Status\n            \"account_pending_approval\": \"حسابك في انتظار الموافقة\",\n            \"wait_admin_approval_desc\": \"يرجى انتظار موافقة الإدارة على حسابك\",\n            // Dashboard\n            \"overview_status\": \"نظرة عامة على حالة\",\n            \"your_requests\": \"طلباتك\",\n            \"assigned_tasks\": \"المهام المخصصة لك\",\n            \"total_requests_desc\": \"إجمالي الطلبات\",\n            \"pending_review_desc\": \"في انتظار المراجعة\",\n            \"approved_today_desc\": \"موافق عليها اليوم\",\n            \"avg_processing_days_desc\": \"متوسط أيام المعالجة\",\n            // Reports\n            \"no_reports_access\": \"ليس لديك صلاحية للوصول إلى التقارير\",\n            \"monthly_report\": \"التقرير الشهري\",\n            \"monthly_stats_desc\": \"إحصائيات الطلبات والموافقات الشهرية\",\n            \"requests_label\": \"الطلبات\",\n            \"approved_label\": \"موافق\",\n            \"rejected_label\": \"مرفوض\",\n            // Requests\n            \"back_button\": \"العودة\",\n            \"request_details\": \"تفاصيل الطلب\",\n            \"download_decision\": \"تحميل القرار\",\n            // Gender and Personal Info\n            \"gender_label\": \"الجنس\",\n            \"male_label\": \"ذكر\",\n            \"female_label\": \"أنثى\",\n            \"marital_status_label\": \"الحالة الاجتماعية\",\n            \"married_label\": \"متزوج\",\n            \"single_label\": \"أعزب\",\n            \"divorced_label\": \"مطلق\",\n            \"widowed_label\": \"أرمل\",\n            // Common UI Text\n            \"or\": \"أو\",\n            \"no_account\": \"ليس لديك حساب؟\",\n            \"demo_accounts\": \"حسابات تجريبية:\",\n            \"system_admin\": \"مسؤول النظام:\",\n            \"applicant\": \"مقدم طلب:\",\n            \"staff_member\": \"موظف:\",\n            // Access Control\n            \"access_denied\": \"غير مصرح\",\n            \"no_beneficiary_access\": \"ليس لديك صلاحية للوصول إلى إدارة المستفيدين\",\n            \"no_registration_access\": \"ليس لديك صلاحية لتسجيل مستفيدين جدد\"\n        }\n    },\n    en: {\n        translation: {\n            // Navigation\n            \"dashboard\": \"Dashboard\",\n            \"profile\": \"Profile\",\n            \"requests\": \"Requests\",\n            \"tasks\": \"Tasks\",\n            \"reports\": \"Reports\",\n            \"settings\": \"Settings\",\n            \"logout\": \"Logout\",\n            // Authentication\n            \"login\": \"Login\",\n            \"register\": \"Register\",\n            \"email\": \"Email\",\n            \"password\": \"Password\",\n            \"full_name\": \"Full Name\",\n            \"national_id\": \"National ID\",\n            \"phone_number\": \"Phone Number\",\n            \"login_tawtheeq\": \"Login with Tawtheeq\",\n            \"login_success\": \"Login successful\",\n            \"login_error\": \"Invalid credentials\",\n            // Profile\n            \"personal_profile\": \"Personal Profile\",\n            \"basic_info\": \"Basic Information\",\n            \"family_info\": \"Family Information\",\n            \"employment_info\": \"Employment Information\",\n            \"financial_info\": \"Financial Information\",\n            \"marital_status\": \"Marital Status\",\n            \"single\": \"Single\",\n            \"married\": \"Married\",\n            \"divorced\": \"Divorced\",\n            \"widowed\": \"Widowed\",\n            // Assistance Requests\n            \"assistance_request\": \"Assistance Request\",\n            \"new_request\": \"New Request\",\n            \"request_type\": \"Request Type\",\n            \"requested_amount\": \"Requested Amount\",\n            \"description\": \"Description\",\n            \"attach_documents\": \"Attach Documents\",\n            \"submit_request\": \"Submit Request\",\n            // Status\n            \"draft\": \"Draft\",\n            \"submitted\": \"Submitted\",\n            \"under_review\": \"Under Review\",\n            \"approved\": \"Approved\",\n            \"rejected\": \"Rejected\",\n            \"pending\": \"Pending\",\n            \"completed\": \"Completed\",\n            // Workflow Stages\n            \"reception_review\": \"Reception Review\",\n            \"researcher_review\": \"Researcher Review\",\n            \"banking_expert_review\": \"Banking Expert Review\",\n            \"department_head_review\": \"Department Head Review\",\n            \"admin_manager_review\": \"Admin Manager Review\",\n            \"minister_review\": \"Minister Review\",\n            // Actions\n            \"approve\": \"Approve\",\n            \"reject\": \"Reject\",\n            \"return\": \"Return\",\n            \"save\": \"Save\",\n            \"edit\": \"Edit\",\n            \"delete\": \"Delete\",\n            \"view\": \"View\",\n            \"download\": \"Download\",\n            \"upload\": \"Upload\",\n            // Common\n            \"welcome\": \"Welcome\",\n            \"loading\": \"Loading...\",\n            \"search\": \"Search\",\n            \"filter\": \"Filter\",\n            \"date\": \"Date\",\n            \"amount\": \"Amount\",\n            \"status\": \"Status\",\n            \"notes\": \"Notes\",\n            \"documents\": \"Documents\",\n            \"history\": \"History\",\n            // Dashboard Stats\n            \"total_requests\": \"Total Requests\",\n            \"pending_review\": \"Pending Review\",\n            \"approved_today\": \"Approved Today\",\n            \"rejected_today\": \"Rejected Today\",\n            \"average_processing_days\": \"Avg. Processing Days\",\n            \"total_users\": \"Total Users\",\n            // User Roles\n            \"zakat_applicant\": \"Zakat Applicant\",\n            \"reception_staff\": \"Reception Staff\",\n            \"researcher\": \"Researcher\",\n            \"banking_expert\": \"Banking Expert\",\n            \"department_head\": \"Department Head\",\n            \"admin_manager\": \"Administration Manager\",\n            \"minister\": \"Minister\",\n            \"system_admin\": \"System Administrator\",\n            // Beneficiary Management\n            \"beneficiaries\": \"Beneficiaries\",\n            \"beneficiary_management\": \"Beneficiary Management\",\n            \"beneficiary_management_desc\": \"Manage and track Zakat and assistance beneficiaries\",\n            \"register_new_beneficiary\": \"Register New Beneficiary\",\n            \"beneficiary_registration\": \"New Beneficiary Registration\",\n            \"beneficiary_registration_desc\": \"Add a new beneficiary to the Zakat management system\",\n            \"beneficiary_list\": \"Beneficiary List\",\n            \"beneficiary_profile\": \"Beneficiary Profile\",\n            \"beneficiary_details\": \"Beneficiary Details\",\n            \"beneficiary_not_found\": \"Beneficiary Not Found\",\n            \"beneficiary_not_found_desc\": \"The requested beneficiary could not be found\",\n            \"back_to_beneficiaries\": \"Back to Beneficiaries List\",\n            // Beneficiary Stats\n            \"total_beneficiaries\": \"Total Beneficiaries\",\n            \"approved_beneficiaries\": \"Approved\",\n            \"under_review_beneficiaries\": \"Under Review\",\n            \"total_distributions\": \"Total Distributions\",\n            \"average_distribution\": \"Average Distribution\",\n            \"pending_verification_count\": \"Pending Verification\",\n            \"needs_review\": \"Needs Review\",\n            \"of_total\": \"of Total\",\n            \"per_beneficiary\": \"per Beneficiary\",\n            // Search and Filters\n            \"search_and_filter\": \"Search and Filter\",\n            \"search_placeholder\": \"Search by name, national ID, or phone number...\",\n            \"filter_by_status\": \"Filter by Status\",\n            \"filter_by_category\": \"Filter by Category\",\n            \"all_statuses\": \"All Statuses\",\n            \"all_categories\": \"All Categories\",\n            \"no_results_found\": \"No results found matching search criteria\",\n            \"showing_results\": \"Showing {{count}} of {{total}} beneficiaries\",\n            \"export\": \"Export\",\n            // Beneficiary Status\n            \"pending_verification\": \"Pending Verification\",\n            \"under_review\": \"Under Review\",\n            \"approved\": \"Approved\",\n            \"rejected\": \"Rejected\",\n            \"suspended\": \"Suspended\",\n            \"inactive\": \"Inactive\",\n            // Zakat Categories\n            \"fuqara\": \"The Poor\",\n            \"masakin\": \"The Needy\",\n            \"amilin\": \"Zakat Administrators\",\n            \"muallafah\": \"Those whose hearts are reconciled\",\n            \"riqab\": \"To free slaves/captives\",\n            \"gharimin\": \"Those in debt\",\n            \"fisabilillah\": \"In the cause of Allah\",\n            \"ibnus_sabil\": \"The wayfarer/traveler\",\n            \"primary_category\": \"Primary Category\",\n            // Table Headers\n            \"name\": \"Name\",\n            \"national_id\": \"National ID\",\n            \"registration_date\": \"Registration Date\",\n            \"total_received\": \"Total Received\",\n            \"actions\": \"Actions\",\n            // Actions\n            \"view_profile\": \"View Profile\",\n            \"edit\": \"Edit\",\n            \"manage_case\": \"Manage Case\",\n            \"generate_voucher\": \"Generate Voucher\",\n            \"back\": \"Back\",\n            // Profile Tabs\n            \"overview\": \"Overview\",\n            \"personal_details\": \"Personal Details\",\n            \"eligibility_verification\": \"Eligibility & Verification\",\n            \"case_management\": \"Case Management\",\n            \"distribution_history\": \"Distribution History\",\n            \"family\": \"Family\",\n            \"documents\": \"Documents\",\n            // Profile Details\n            \"eligibility_score\": \"Eligibility Score\",\n            \"high_score\": \"High Score\",\n            \"family_size\": \"Family Size\",\n            \"dependents\": \"Dependents\",\n            \"last_distribution\": \"Last Distribution\",\n            \"no_distribution\": \"None\",\n            \"not_distributed_yet\": \"Not distributed yet\",\n            \"distributions\": \"Distributions\",\n            // Contact Information\n            \"contact_info\": \"Contact Information\",\n            \"account_status\": \"Account Status\",\n            \"current_status\": \"Current Status\",\n            \"registration_date\": \"Registration Date\",\n            \"next_review\": \"Next Review\",\n            // Personal Information\n            \"personal_information\": \"Personal Information\",\n            \"basic_information\": \"Basic Information\",\n            \"name_arabic\": \"Name in Arabic\",\n            \"name_english\": \"Name in English\",\n            \"date_of_birth\": \"Date of Birth\",\n            \"gender\": \"Gender\",\n            \"male\": \"Male\",\n            \"female\": \"Female\",\n            \"marital_status\": \"Marital Status\",\n            \"single\": \"Single\",\n            \"married\": \"Married\",\n            \"divorced\": \"Divorced\",\n            \"widowed\": \"Widowed\",\n            // Family Members\n            \"family_members\": \"Family Members\",\n            \"no_family_info\": \"No family information available\",\n            \"dependent\": \"Dependent\",\n            \"special_needs\": \"Special Needs\",\n            \"relationship\": \"Relationship\",\n            \"age\": \"Age\",\n            \"years\": \"years\",\n            \"son\": \"Son\",\n            \"daughter\": \"Daughter\",\n            \"mother\": \"Mother\",\n            \"father\": \"Father\",\n            // Documents\n            \"no_documents\": \"No documents uploaded\",\n            \"verified\": \"Verified\",\n            \"pending_verification\": \"Pending Verification\",\n            \"upload_date\": \"Upload Date\",\n            // Coming Soon\n            \"coming_soon\": \"Coming Soon\",\n            \"under_development\": \"Under Development\",\n            \"registration_form_coming\": \"Registration Form Under Development\",\n            \"registration_form_desc\": \"New beneficiary registration form will be added soon\",\n            \"will_include_features\": \"The form will include the following features:\",\n            \"multi_step_form\": \"Multi-step form with progress indicator\",\n            \"dual_language_input\": \"Personal data input in Arabic and English\",\n            \"zakat_categories_selection\": \"Selection of eight Zakat categories\",\n            \"document_upload\": \"Required document upload\",\n            \"data_validation\": \"Data validation\",\n            \"duplicate_detection\": \"Automatic duplicate detection\",\n            // System Information\n            \"system_name\": \"Zakat Management System\",\n            \"system_description\": \"Comprehensive system for managing Zakat and assistance requests\",\n            // Authentication Messages\n            \"create_new_account\": \"Create new account\",\n            \"sign_in_to_account\": \"Sign in to your account\",\n            \"choose_login_method\": \"Choose the appropriate login method\",\n            \"verifying\": \"Verifying...\",\n            \"error\": \"Error\",\n            \"passwords_not_match\": \"Passwords do not match\",\n            \"account_created_success\": \"Account created successfully\",\n            \"wait_admin_approval\": \"Please wait for admin approval of your account\",\n            \"account_creation_error\": \"Error creating account\",\n            \"unexpected_error\": \"An unexpected error occurred\",\n            \"error_during_creation\": \"An error occurred during account creation\",\n            \"invalid_credentials\": \"Invalid credentials entered\",\n            \"welcome_to_system\": \"Welcome to the Zakat Management System\",\n            \"error_during_login\": \"An error occurred during login\",\n            // Account Status\n            \"account_pending_approval\": \"Your account is pending approval\",\n            \"wait_admin_approval_desc\": \"Please wait for admin approval of your account\",\n            // Dashboard\n            \"overview_status\": \"Overview of status\",\n            \"your_requests\": \"your requests\",\n            \"assigned_tasks\": \"tasks assigned to you\",\n            \"total_requests_desc\": \"Total requests\",\n            \"pending_review_desc\": \"Pending review\",\n            \"approved_today_desc\": \"Approved today\",\n            \"avg_processing_days_desc\": \"Average processing days\",\n            // Reports\n            \"no_reports_access\": \"You do not have permission to access reports\",\n            \"monthly_report\": \"Monthly Report\",\n            \"monthly_stats_desc\": \"Monthly statistics of requests and approvals\",\n            \"requests_label\": \"Requests\",\n            \"approved_label\": \"Approved\",\n            \"rejected_label\": \"Rejected\",\n            // Requests\n            \"back_button\": \"Back\",\n            \"request_details\": \"Request Details\",\n            \"download_decision\": \"Download Decision\",\n            // Gender and Personal Info\n            \"gender_label\": \"Gender\",\n            \"male_label\": \"Male\",\n            \"female_label\": \"Female\",\n            \"marital_status_label\": \"Marital Status\",\n            \"married_label\": \"Married\",\n            \"single_label\": \"Single\",\n            \"divorced_label\": \"Divorced\",\n            \"widowed_label\": \"Widowed\",\n            // Common UI Text\n            \"or\": \"or\",\n            \"no_account\": \"Don't have an account?\",\n            \"demo_accounts\": \"Demo Accounts:\",\n            \"system_admin\": \"System Admin:\",\n            \"applicant\": \"Applicant:\",\n            \"staff_member\": \"Staff Member:\",\n            // Access Control\n            \"access_denied\": \"Access Denied\",\n            \"no_beneficiary_access\": \"You do not have permission to access beneficiary management\",\n            \"no_registration_access\": \"You do not have permission to register new beneficiaries\"\n        }\n    }\n};\n// Initialize i18n immediately with resources\ni18next__WEBPACK_IMPORTED_MODULE_0__[\"default\"].use(i18next_browser_languagedetector__WEBPACK_IMPORTED_MODULE_2__[\"default\"]).use(react_i18next__WEBPACK_IMPORTED_MODULE_1__.initReactI18next).init({\n    resources,\n    fallbackLng: \"ar\",\n    lng: \"ar\",\n    debug: \"development\" === \"development\",\n    detection: {\n        order: [\n            \"localStorage\",\n            \"navigator\",\n            \"htmlTag\"\n        ],\n        caches: [\n            \"localStorage\"\n        ]\n    },\n    interpolation: {\n        escapeValue: false\n    }\n});\n/* harmony default export */ __webpack_exports__[\"default\"] = (i18next__WEBPACK_IMPORTED_MODULE_0__[\"default\"]);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/i18n.ts\n"));

/***/ })

});